#!/usr/bin/env python3
"""
Debug script for AE WriteOut Generator
This script helps identify why the AE WriteOut generator output might be disappearing.
"""

import sys
import os
from ae_writeout_generator import AEWriteOutGenerator

def debug_ae_writeout_generator(als_file_path, tumor_type="Solid Tumor", study_id="test_study"):
    """Debug the AE WriteOut generator step by step."""
    
    print("=" * 60)
    print("AE WRITEOUT GENERATOR DEBUG")
    print("=" * 60)
    
    # Check if ALS file exists
    if not os.path.exists(als_file_path):
        print(f"❌ ERROR: ALS file not found: {als_file_path}")
        return False
    
    print(f"✅ ALS file found: {als_file_path}")
    print(f"📊 Tumor type: {tumor_type}")
    print(f"🔬 Study ID: {study_id}")
    print()
    
    try:
        # Step 1: Initialize the generator
        print("Step 1: Initializing AEWriteOutGenerator...")
        generator = AEWriteOutGenerator(als_file_path, tumor_type)
        print("✅ Generator initialized successfully")
        print()
        
        # Step 2: Check the dataframes
        print("Step 2: Checking generated dataframes...")
        print(f"AE writeout dataframe shape: {generator.ae_writeout_df.shape}")
        print(f"AE writeout dataframe columns: {list(generator.ae_writeout_df.columns)}")
        
        if not generator.ae_writeout_df.empty:
            print("Sample data from ae_writeout_df:")
            print(generator.ae_writeout_df.head())
        else:
            print("⚠️  WARNING: ae_writeout_df is empty!")
        print()
        
        # Step 3: Check table mappings
        print("Step 3: Checking table mappings...")
        print(f"Table mappings: {generator.table_mappings}")
        print()
        
        # Step 4: Test variable mapping
        print("Step 4: Testing AE variable mapping...")
        ae_vars = generator._get_ae_variable_mapping()
        print(f"AE variable mappings: {ae_vars}")
        print()
        
        # Step 5: Test drug action mappings
        print("Step 5: Testing drug action mappings...")
        drug_actions = generator._get_drug_action_mappings()
        print(f"Number of drug actions found: {len(drug_actions)}")
        for i, action in enumerate(drug_actions):
            print(f"  Drug {i+1}: {action}")
        print()
        
        # Step 6: Generate the function
        print("Step 6: Generating R function...")
        result = generator.generate_function(study_id)
        
        if result:
            print(f"✅ Function generated successfully!")
            print(f"📏 Generated code length: {len(result)} characters")
            print(f"📝 First 200 characters:")
            print(result[:200] + "..." if len(result) > 200 else result)
            
            # Save to file for inspection
            output_file = f"debug_ae_writeout_{study_id}.R"
            with open(output_file, 'w') as f:
                f.write(result)
            print(f"💾 Full output saved to: {output_file}")
            
        else:
            print("❌ ERROR: Function generation returned empty result!")
            return False
            
        print()
        print("✅ All steps completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ ERROR during debugging: {str(e)}")
        import traceback
        print("Full traceback:")
        print(traceback.format_exc())
        return False

def main():
    """Main function to run the debug script."""
    
    # Default values - you can modify these
    default_als_file = "BGB-B455-101_V2.0_CC1_31Mar2025_1515.xls.xlsx"  # Update this path
    default_tumor_type = "Solid Tumor"
    default_study_id = "b_bgb_455_101"
    
    # Check command line arguments
    if len(sys.argv) > 1:
        als_file = sys.argv[1]
    else:
        als_file = default_als_file
        
    if len(sys.argv) > 2:
        tumor_type = sys.argv[2]
    else:
        tumor_type = default_tumor_type
        
    if len(sys.argv) > 3:
        study_id = sys.argv[3]
    else:
        study_id = default_study_id
    
    # Run the debug
    success = debug_ae_writeout_generator(als_file, tumor_type, study_id)
    
    if success:
        print("\n🎉 Debug completed successfully!")
    else:
        print("\n💥 Debug failed - check the error messages above")
        sys.exit(1)

if __name__ == "__main__":
    main()
