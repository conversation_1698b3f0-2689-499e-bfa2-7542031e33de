import pandas as pd
import re
from typing import Dict, List, Tuple

class AEWriteOutGenerator:
    def __init__(self, als_file, tumor_type=None):
        """Initialize the AEWriteOutGenerator with ALS file and tumor type.

        Args:
            als_file: Path to the ALS file
            tumor_type: Type of tumor ('Solid Tumor' or 'Heme')
        """
        print(f"Initializing AEWriteOutGenerator with ALS file: {als_file}, tumor_type: {tumor_type}")
        self.als_file = als_file
        self.tumor_type = tumor_type
        self.ae_writeout_df = self._generate_ae_writeout_mapping()
        print(f"AE writeout dataframe shape: {self.ae_writeout_df.shape}")
        self.table_mappings = self._generate_table_mappings()
        print(f"Table mappings: {self.table_mappings}")

    def _generate_ae_writeout_mapping(self) -> pd.DataFrame:
        """Generate mapping dataframe for AE WriteOut fields from ALS file."""
        try:
            # Read Fields sheet from ALS file
            fields_df = pd.read_excel(self.als_file, sheet_name="Fields")
            fields_df['Form'] = fields_df['FormOID'].str.lower()
            fields_df = fields_df[['Form', 'FieldOID', 'SASLabel', 'PreText']]
            fields_df = fields_df.dropna(subset=['Form'])

            # Read Forms sheet from ALS file
            forms_df = pd.read_excel(self.als_file, sheet_name="Forms")
            forms_df['Form'] = forms_df['OID'].str.lower()
            forms_df = forms_df[['Form', 'DraftFormName']]
            forms_df = forms_df.dropna(subset=['Form'])

            # Merge fields and forms
            merged_df = pd.merge(fields_df, forms_df, on='Form', how='left')

            # Fill NaN SASLabel with PreText
            merged_df['SASLabel'] = merged_df['SASLabel'].fillna(merged_df['PreText'])

            # Filter for AE fields
            ae_df = merged_df[merged_df['DraftFormName'].str.contains('^Adverse Events$|^Adverse Events \{Mixed form\}$', case=False, na=False)]

            # Extract drug names and action fields
            ae_action_df = ae_df[ae_df['SASLabel'].str.contains('Action taken with|Dose not changed|Dose reduced|Drug withdrawn|Drug interrupted|Dose rate reduced|Not applicable', na=False)]

            # Extract drug names from "Action taken with" fields
            ae_action_df['Drug'] = ae_action_df.apply(
                lambda row: re.sub('.*with\\s+(.*?)\\s*\\(check all that apply\\).*', '\\1', row['SASLabel'])
                if pd.notna(row['SASLabel']) and 'Action taken with' in row['SASLabel']
                else None,
                axis=1
            )

            # Extract group flags to associate actions with drugs
            ae_action_df['GroupFlag'] = ae_action_df['FieldOID'].apply(
                lambda x: int(re.sub('.*?(\\d+)$', '\\1', x)) if pd.notna(x) and re.search('\\d+$', x) else 0
            )

            # Forward and backward fill drug names within groups
            ae_action_df = ae_action_df.sort_values('GroupFlag')
            ae_action_df['Drug'] = ae_action_df.groupby('GroupFlag')['Drug'].transform(
                lambda x: x.fillna(method='ffill').fillna(method='bfill')
            )

            return ae_action_df
        except Exception as e:
            print(f"Error generating AE WriteOut mapping: {str(e)}")
            return pd.DataFrame()

    def _generate_table_mappings(self) -> Dict[str, str]:
        """Generate mappings for table names from ALS file."""
        try:
            # Read Forms sheet from ALS file
            forms_df = pd.read_excel(self.als_file, sheet_name="Forms")

            # Create mappings for different tables
            mappings = {
                'ae': forms_df[forms_df['DraftFormName'].str.contains('^Adverse Events$|^Adverse Events \{Mixed form\}$', case=False, na=False)]['OID'].iloc[0].lower() if not forms_df[forms_df['DraftFormName'].str.contains('^Adverse Events$|^Adverse Events \{Mixed form\}$', case=False, na=False)].empty else ' ',
            }

            return mappings
        except Exception as e:
            print(f"Error generating table mappings: {str(e)}")
            return {}

    def _get_ae_variable_mapping(self) -> Dict[str, str]:
        """Get variable mappings for AE fields from ALS file."""
        try:
            # Read Fields and Forms sheets from ALS file
            fields_df = pd.read_excel(self.als_file, sheet_name="Fields")
            fields_df['Form'] = fields_df['FormOID'].str.lower()
            fields_df = fields_df[['Form', 'FieldOID', 'SASLabel', 'PreText']]
            fields_df = fields_df.dropna(subset=['Form'])

            forms_df = pd.read_excel(self.als_file, sheet_name="Forms")
            forms_df['Form'] = forms_df['OID'].str.lower()
            forms_df = forms_df[['Form', 'DraftFormName']]
            forms_df = forms_df.dropna(subset=['Form'])

            # Join fields and forms
            var_label_form = fields_df.merge(forms_df, on='Form')

            # Fill NaN SASLabel with PreText
            var_label_form['SASLabel'] = var_label_form['SASLabel'].fillna(var_label_form['PreText'])

            # Filter for AE fields
            ae_std = var_label_form[
                var_label_form['DraftFormName'].str.contains('^Adverse Events$|^Adverse Events \{Mixed form\}$', case=False, na=False) &
                var_label_form['SASLabel'].str.contains('Start date|Stop date|^Adverse event$|Toxicity grade|Result in death|Was adverse event serious|Outcome|dose limiting toxicity|clinical interest|special interest|immune-mediated|immune-related|infusion related', case=False, na=False) &
                ~var_label_form['SASLabel'].str.contains('RSG', case=False, na=False)
            ].copy()

            # Create mapping dictionary
            mapping = {}
            for _, row in ae_std.iterrows():
                sas_label = row['SASLabel']
                field_oid = row['FieldOID']

                # Map based on SASLabel patterns
                if pd.notna(sas_label):
                    if re.search(r'^Adverse event$', sas_label, re.IGNORECASE):
                        mapping['Adverse event'] = field_oid
                    elif re.search(r'Toxicity grade', sas_label, re.IGNORECASE):
                        mapping['Toxicity grade'] = field_oid

            return mapping
        except Exception as e:
            print(f"Error getting AE variable mapping: {str(e)}")
            return {}

    def _get_drug_action_mappings(self) -> List[Dict[str, str]]:
        """Get mappings for drug action fields."""
        drug_actions = []

        # Group by drug and collect action fields
        for drug, group in self.ae_writeout_df.groupby('Drug'):
            if pd.isna(drug):
                continue

            actions = {}
            actions['drug'] = drug

            # Extract action fields for this drug
            for _, row in group.iterrows():
                if 'Dose not changed' in row['SASLabel']:
                    actions['no'] = row['FieldOID']
                elif 'Dose reduced' in row['SASLabel']:
                    actions['dr'] = row['FieldOID']
                elif 'Drug withdrawn' in row['SASLabel']:
                    actions['dw'] = row['FieldOID']
                elif 'Drug interrupted' in row['SASLabel']:
                    actions['di'] = row['FieldOID']
                elif 'Dose rate reduced' in row['SASLabel']:
                    actions['rd'] = row['FieldOID']
                elif 'Not applicable' in row['SASLabel']:
                    actions['na'] = row['FieldOID']

            drug_actions.append(actions)

        return drug_actions

    def generate_function(self, study_id: str) -> str:
        """Generate the aeWriteOut R function."""
        try:
            print(f"Starting AE WriteOut function generation for study_id: {study_id}")

            # Get drug action mappings
            drug_actions = self._get_drug_action_mappings()
            print(f"Drug actions found: {len(drug_actions)} drugs")

            # Get table mappings
            ae_table = self.table_mappings.get('ae', 'ae')
            print(f"AE table mapping: {ae_table}")

            # Get AE variable mappings
            ae_vars = self._get_ae_variable_mapping()
            print(f"AE variable mappings: {ae_vars}")

            ae_tox_grade = ae_vars.get('Toxicity grade', 'AETOXGR')
            ae_term = ae_vars.get('Adverse event', 'AETERM')

            # For AETERM_PT, we need to use the ae_term with _PT suffix
            ae_term_pt = f"{ae_term}_PT" if ae_term != 'AETERM' else 'AETERM_PT'

            print(f"AE variables extracted: ae_tox_grade={ae_tox_grade}, ae_term={ae_term}, ae_term_pt={ae_term_pt}")

            # Check if we have the minimum required data
            if not drug_actions:
                print("Warning: No drug actions found in ALS file")
                # Create a default drug action to prevent empty template
                drug_actions = [{'drug': 'Study Drug', 'no': 'AENO', 'dr': 'AEDR', 'dw': 'AEDW', 'di': 'AEDI'}]
                print("Using default drug actions")

            if not ae_vars:
                print("Warning: No AE variable mappings found, using defaults")

            # Template for the R function with placeholders
            template = f"""#' @title ae_5_AEWriteOut_{study_id}
#' @description Merge ae calculations into the original ae crf and write out to the analysis folder
#'
#' @param studyId This is a character field, studyId
#' @param sourceLocation This is a character field, study analysis directory
#' @param tempLogger This is the logger for the whole job, creating one logfile per jobdatetime
#' @param jobdatetime This is a character field, datetime of the jobrun, format Y_m_d_H_M_OS3
#' @param ae This the ae dataframe
#' @param ae_to_lab_toxflag This is the ae_to_lab_toxflag dataframe
#' @param ae_to_vs_toxflag This is the ae_to_vs_toxflag dataframe
#' @param ae_CMList This is the ae_CMList dataframe
#' @param SAEFlag This is the SAEFlag dataframe
#' @param MHFlag This is the MHFlag dataframe
#' @param SubjectInfo This is the SubjectInfo dataframe
#' @param develop.f disable email notification for testing
#' @param vpath This is the location of vault
#'
#' @return ae_calc
#' @return Treated
#'
#' @export ae_5_AEWriteOut_{study_id}
#'
#' @importFrom dplyr filter mutate select rowwise n_distinct distinct group_by summarise left_join rename
#' @importFrom checkmate assert_data_frame assert_directory_exists
#' @importFrom log4r error info warn
#' @importFrom mailR send.mail
#' @importFrom secret get_secret local_key
#' @importFrom magrittr %>%
#' @importFrom writexl write_xlsx
#' @importFrom GSDSUtilities raiseLabels
#' @importFrom rlang .data
#'
#' @examples
#' \\dontrun{{
#' ae_5_AEWriteOut_{study_id}(studyId = studyIdVar, sourceLocation = OutFolder,
#'                                tempLogger = CentralLogger, jobdatetime = jobdatetime,
#'                                ae = {ae_table}, ae_to_lab_toxflag = ae_to_lab_toxflag,
#'                                ae_to_vs_toxflag = ae_to_vs_toxflag,
#'                                ae_CMList = ae_CMList, SAEFlag = SAEFlag, MHFlag = MHFlag, SubjectInfo = SubjectInfo,
#'                                develop.f = develop.f, vpath = vpath)
#' }}
#'
#'
ae_5_AEWriteOut_{study_id} <- function(studyId, sourceLocation, tempLogger, jobdatetime = jobdatetime,
                                           ae, ae_to_lab_toxflag, ae_to_vs_toxflag, ae_CMList, SAEFlag, MHFlag,
                                           SubjectInfo, develop.f = develop.f, vpath = vpath) {{
  withCallingHandlers(
    expr = {{
      calledFun = "ae_5_AEWriteOut_{study_id}"
      # Start Function --------------------------------------------------------------------
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- Started."))

      # Assert sourceLocation directory exists
      checkmate::assert_directory_exists(sourceLocation)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," The sourceLocation directory exists."))
      # Assert ae has min.rows and min.cols
      checkmate::assert_data_frame(ae, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm ae has min.rows and min.cols."))
      # Assert ae_to_lab_toxflag has min.rows and min.cols
      checkmate::assert_data_frame(ae_to_lab_toxflag, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm ae_to_lab_toxflag has min.rows and min.cols."))
      # Assert ae_CMList has min.rows and min.cols
      checkmate::assert_data_frame(ae_CMList, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm ae_CMList has min.rows and min.cols."))
      # Assert SAEFlag has min.rows and min.cols
      checkmate::assert_data_frame(SAEFlag, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm SAEFlag has min.rows and min.cols."))
      # Assert MHFlag has min.rows and min.cols
      checkmate::assert_data_frame(MHFlag, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm MHFlag has min.rows and min.cols."))


      # ae_calc Dataframe  -------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating ae_calc dataframe"))

      #TreatedFlag & Total/Site Treated Calculations

      Treated <- SubjectInfo %>%
        dplyr::filter(!is.na(.data$FirstDoseDate)) %>%
        dplyr::select(.data$`Subject name or identifier`, .data$SiteNumber) %>%
        dplyr::mutate(TotalTreated = dplyr::n_distinct(.data$`Subject name or identifier`),
                      Treated = "Yes") %>%
        dplyr::distinct()


      SiteTreated <- Treated %>%
        dplyr::group_by(.data$SiteNumber) %>%
        dplyr::summarise(SiteTreated = dplyr::n_distinct(.data$`Subject name or identifier`)) %>%
        dplyr::distinct()
"""

            # Add CCQ list section if tumor type is Solid Tumor
            if self.tumor_type == "Solid Tumor":
                template += """
      # Merge in CCQ list and create a CCQ Flag and add imAE Category
      CCQ_lst <- readxl::read_xlsx("/usrfiles/spotfire/IntegratedSafety/Dictionary/CCQ MedDRA27.0.xlsx", sheet = "CCQ v27.0") %>%
        dplyr::filter(.data$Type=="imAE") %>%
        dplyr::select(.data$`CCQ Category`, AEPT = tidyselect::contains("PT"), .data$Scope) %>%
        dplyr::distinct() %>%
        dplyr::mutate(CCQ_flag = "Y")
"""

            # Add ae_calc dataframe creation
            template += f"""
      #Create ae_calc dataframe
      ae_calc <- dplyr::left_join(ae, ae_to_lab_toxflag, by = c("Subject","RecordId"))  %>%
        dplyr::left_join(ae_CMList, by = c("RecordId")) %>%
        dplyr::left_join(ae_to_vs_toxflag, by = c("Subject","RecordId")) %>%
        dplyr::left_join(SAEFlag, by = c("Subject","RecordId")) %>%
        dplyr::left_join(MHFlag, by = c("Subject","RecordId")) %>%
        dplyr::left_join(Treated %>% dplyr::select(-"SiteNumber"), by = c("Subject" = "Subject name or identifier")) %>%
        dplyr::left_join(SiteTreated, by = c("SiteNumber")) %>%
        #UpdateVars
        dplyr::mutate(`Total#Grade3-5Cases` = ae %>% dplyr::filter(grepl("[3-5]", .data${ae_tox_grade}), !is.na(.data${ae_term})) %>%
                        nrow() ) %>%
        dplyr::rename(CMList = .data$CMList_Calc)
"""

            # Add CCQ join if tumor type is Solid Tumor
            if self.tumor_type == "Solid Tumor":
                template += f"""
      ae_calc <- dplyr::left_join(ae_calc, CCQ_lst, by = c("{ae_term_pt}" = "AEPT")) %>%
        dplyr::mutate(CCQ_flag = dplyr::if_else(is.na(.data$CCQ_flag), "N", .data$CCQ_flag))
"""

            # Add drug names for AE actions taken
            template += """
      #Add drug names for AE actions taken ----
      #UpdateVars
"""

            # Add drug action labels
            for i, drug_action in enumerate(drug_actions, 1):
                drug_name = drug_action.get('drug', f"Drug {i}")

                if 'no' in drug_action:
                    template += f'      attr(ae_calc${drug_action["no"]}, "label") <- "Dose not changed ({drug_name})"\n'
                if 'dr' in drug_action:
                    template += f'      attr(ae_calc${drug_action["dr"]}, "label") <- "Dose reduced ({drug_name})"\n'
                if 'dw' in drug_action:
                    template += f'      attr(ae_calc${drug_action["dw"]}, "label") <- "Drug withdrawn ({drug_name})"\n'
                if 'di' in drug_action:
                    template += f'      attr(ae_calc${drug_action["di"]}, "label") <- "Drug interrupted ({drug_name})"\n'
                if 'rd' in drug_action:
                    template += f'      attr(ae_calc${drug_action["rd"]}, "label") <- "Dose rate reduced ({drug_name})"\n'
                if 'na' in drug_action:
                    template += f'      attr(ae_calc${drug_action["na"]}, "label") <- "Not applicable ({drug_name})"\n'

                template += "\n"

            # Add final part of the template
            template += """
     # add analysis output to list before raiseLabels
      ae_list <- list("ae" = ae_calc)
      assign("ae_list", ae_list, envir = parent.frame())

     # raiseLabels on ae_calc Dataframe  -------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","RaiseLabels on ae_calc dataframe"))
      ae_calc <- GSDSUtilities::raiseLabels(ae_calc, "label", isNullC = NA)

      # Assign ae_calc to calling envir ----------------------------------------------------
      assign("ae_calc", ae_calc, envir = parent.frame())
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," ae_calc returned"))

      # Assign Treated to calling envir ----------------------------------------------------
      assign("Treated", Treated , envir = parent.frame())
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Treated returned"))

      # End of aeCalc Function ----------------------------------------------------
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))
    },

    error = function(e){
      #Log that error was caught, print error, log detail error condition, and stop
      log4r::error(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,": Caught an error!"))
      log4r::error(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun," Error : ", conditionMessage(e)))
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))
      if (is.null(develop.f)) {
        mailR::send.mail(from = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath)$user.name,
                         to = secret::get_secret("mail_to", key = secret::local_key(), vault = vpath),
                         subject = paste0("MDR Transformation Function: ", calledFun, " Failure at jobdatetime: ",jobdatetime),
                         body = paste0("<html><body><p>StudyId: ", studyId, " </p><p>Error DateTime: ",
                                       format(Sys.time(), "%Y-%m-%d %X %Z"), "</p><p>Error : ", conditionMessage(e),"</p></body></html>"),
                         html = TRUE,
                         smtp = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath),
                         authenticate = TRUE,
                         send = TRUE)
      }
      stop(paste0("Failure in function :", calledFun))
    },
    warning = function(w){
      # Log that warning was caught, print warning, log detail warning condition
      log4r::warn(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,": Caught a warning!"))
      log4r::warn(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun," Warning : ", conditionMessage(w)))
      invokeRestart("muffleWarning")
    }
  )
}
"""
            print(f"Successfully generated AE WriteOut function. Template length: {len(template)} characters")
            return template
        except Exception as e:
            print(f"Error generating AE WriteOut function: {str(e)}")
            print(f"Exception type: {type(e).__name__}")
            import traceback
            print(f"Traceback: {traceback.format_exc()}")
            return ""
