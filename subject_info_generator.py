import pandas as pd
import re
from typing import Dict, List, Tuple

class SubjectInfoGenerator:
    def __init__(self, als_file, tumor_type=None):
        """Initialize the SubjectInfoGenerator with ALS file and tumor type.

        Args:
            als_file: The ALS file uploaded by the user
            tumor_type: The tumor type selected by the user (He<PERSON> or <PERSON> Tumor)
        """
        self.als_file = als_file
        self.tumor_type = tumor_type
        self.subject_info_df = self._generate_subject_info_mapping()
        self.table_mappings = self._generate_table_mappings()

    def _generate_table_mappings(self) -> Dict[str, any]:
        """Generate mappings between standard table names and actual table names from ALS."""
        try:
            # Read ALS file sheets
            fields_df = pd.read_excel(self.als_file, sheet_name="Fields", engine='openpyxl')
            forms_df = pd.read_excel(self.als_file, sheet_name="Forms", engine='openpyxl')

            # Process forms to get table mappings
            forms_df = forms_df[['OID', 'DraftFormName']].copy()

            # For rs_r and mh_dx, we need to get all matching tables, not just the first one
            rs_r_tables = forms_df[forms_df['DraftFormName'].str.contains('Disease Assessment|Time-point Response Assessment', case=False, na=False)]['OID'].tolist()
            rs_r_tables = [oid.lower() for oid in rs_r_tables] if rs_r_tables else ['rs_r']

            mh_dx_tables = forms_df[forms_df['DraftFormName'].str.contains('Disease History', case=False, na=False)]['OID'].tolist()
            mh_dx_tables = [oid.lower() for oid in mh_dx_tables] if mh_dx_tables else ['mh_dx']

            # Store the table names for later use
            self.rs_r_tables = rs_r_tables
            self.mh_dx_tables = mh_dx_tables

            # For backward compatibility, use the first table as the default
            rs_r_default = rs_r_tables[0] if rs_r_tables else ' '
            mh_dx_default = mh_dx_tables[0] if mh_dx_tables else ' '

            # Map standard names to actual form names and convert to lowercase
            table_mappings = {
                'ds_enr': forms_df[forms_df['DraftFormName'].str.contains('Enrollment', case=False, na=False)]['OID'].iloc[0].lower() if not forms_df[forms_df['DraftFormName'].str.contains('Enrollment', case=False, na=False)].empty else ' ',
                'subject': forms_df[forms_df['DraftFormName'].str.contains('Subject', case=False, na=False)]['OID'].iloc[0].lower() if not forms_df[forms_df['DraftFormName'].str.contains('Subject', case=False, na=False)].empty else ' ',
                'sd': forms_df[forms_df['DraftFormName'].str.contains('End of Study', case=False, na=False)]['OID'].iloc[0].lower() if not forms_df[forms_df['DraftFormName'].str.contains('End of Study', case=False, na=False)].empty else ' ',
                'dd': forms_df[forms_df['DraftFormName'].str.contains('Death', case=False, na=False)]['OID'].iloc[0].lower() if not forms_df[forms_df['DraftFormName'].str.contains('Death', case=False, na=False)].empty else ' ',
                'dm': forms_df[forms_df['DraftFormName'].str.contains('Demographics', case=False, na=False)]['OID'].iloc[0].lower() if not forms_df[forms_df['DraftFormName'].str.contains('Demographics', case=False, na=False)].empty else ' ',
                'rs_r': rs_r_default,
                'mh_dx': mh_dx_default,
                # Store all tables for rs_r and mh_dx
                'rs_r_tables': rs_r_tables,
                'mh_dx_tables': mh_dx_tables
            }

            return table_mappings

        except Exception as e:
            raise Exception(f"Error generating table mappings: {str(e)}")

    def _generate_subject_info_mapping(self):
        """Generate subject info mapping from ALS file."""
        try:
            # Read ALS file sheets
            fields_df = pd.read_excel(self.als_file, sheet_name="Fields", engine='openpyxl')
            forms_df = pd.read_excel(self.als_file, sheet_name="Forms", engine='openpyxl')

            # Process fields
            fields_df = fields_df[['FormOID', 'FieldOID', 'SASLabel']].copy()
            fields_df['Form'] = fields_df['FormOID'].str.lower()
            fields_df = fields_df.drop('FormOID', axis=1).dropna()

            # Process forms
            forms_df = forms_df[['OID', 'DraftFormName']].copy()
            forms_df['Form'] = forms_df['OID'].str.lower()
            forms_df = forms_df.drop('OID', axis=1).dropna()

            # Join fields and forms
            var_label_form = fields_df.merge(forms_df, on='Form')

            # Generate mappings for each summary table
            enr_summary = var_label_form[
                var_label_form['DraftFormName'].str.contains('Enrollment', case=False, na=False) &
                var_label_form['SASLabel'].str.contains('Was the subject enrolled|^Phase|^Cohort|^Part|Dose level', case=False, na=False)
            ].copy()
            enr_summary['part'] = 'enr_summary'

            subject_summary = var_label_form[
                var_label_form['DraftFormName'].str.contains('Subject', case=False, na=False) &
                var_label_form['SASLabel'].str.contains('Country', case=False, na=False)
            ].copy()
            subject_summary['part'] = 'subject_summary'

            sd_summary = var_label_form[
                var_label_form['DraftFormName'].str.contains('End of Study', case=False, na=False) &
                var_label_form['SASLabel'].str.contains('Date of subject completion/discontinuation from the study|Indicate primary reason for subject discontinuation from the study|Other', case=False, na=False)
            ].copy()
            sd_summary['part'] = 'sd_summary'

            dd_summary = var_label_form[
                var_label_form['DraftFormName'].str.contains('Death', case=False, na=False) &
                var_label_form['SASLabel'].str.contains('Date of death|Primary cause of death|Other', case=False, na=False)
            ].copy()
            dd_summary['part'] = 'dd_summary'

            dm_summary = var_label_form[
                var_label_form['DraftFormName'].str.contains('Demographics', case=False, na=False) &
                var_label_form['SASLabel'].str.contains('Age|Sex|Is subject of child-bearing potential|Ethnicity|American Indian or Alaska Native|^Asian$|Black or African American|Native Hawaiian or other Pacific Islander|^White$|^Other$|Not reported|Unknown', case=False, na=False)
            ].copy()
            dm_summary['part'] = 'dm_summary'

            # Create rsrc_summary with multiple possible field names
            rsrc_date_patterns = ['Date of response', 'Date of overall response']
            rsrc_pattern = '|'.join(rsrc_date_patterns)
            rsrc_summary = var_label_form[
                var_label_form['DraftFormName'].str.contains('Disease Assessment|Time-point Response Assessment', case=False, na=False) &
                (var_label_form['SASLabel'].str.contains(rsrc_pattern, case=False, na=False))
            ].copy()
            rsrc_summary['part'] = 'rsrc_summary'

            # Create candig_summary with multiple possible field names
            candig_term_patterns = ['Medical history term', 'Type of solid tumor', 'Disease type at study entry','Disease type of initial diagnosis']
            candig_pattern = '|'.join(candig_term_patterns)
            candig_summary = var_label_form[
                var_label_form['DraftFormName'].str.contains('Disease History', case=False, na=False) &
                (var_label_form['SASLabel'].str.contains(candig_pattern, case=False, na=False))
            ].copy()
            candig_summary['part'] = 'candig_summary'

            # Combine all mappings
            subject_info_df = pd.concat([
                enr_summary, subject_summary, sd_summary,
                dd_summary, dm_summary, rsrc_summary, candig_summary
            ], ignore_index=True)

            # Add treatment column
            # Handle both formats: "Study Drug Administration - treatment" and "Study Drug Administration Treatment"
            # Treatment names can contain dashes (e.g., "BG-60366")
            study_drug_forms = var_label_form[
                var_label_form['DraftFormName'].str.contains('Study Drug Administration', case=False, na=False)
            ]

            treatments = []
            for form_name in study_drug_forms['DraftFormName'].unique():
                # Check if it starts with "Study Drug Administration"
                if form_name.lower().startswith('study drug administration'):
                    # Find the position after "Study Drug Administration"
                    prefix_len = len('Study Drug Administration')
                    remainder = form_name[prefix_len:].strip()

                    # If remainder starts with " - ", remove it
                    if remainder.startswith(' - '):
                        treatment = remainder[3:].strip()  # Remove " - " (3 characters)
                    elif remainder.startswith('-'):
                        treatment = remainder[1:].strip()  # Remove "-" (1 character)
                    else:
                        treatment = remainder  # Direct format like "Study Drug Administration BG-60366"
                else:
                    # Fallback: use the whole form name after "Study Drug Administration"
                    treatment = form_name.replace('Study Drug Administration', '').strip()

                if treatment:  # Only add non-empty treatments
                    treatments.append(treatment)

            treatments = list(set(treatments))  # Remove duplicates

            # Store the first treatment in the treatment column for backward compatibility
            subject_info_df['treatment'] = treatments[0] if len(treatments) > 0 else None
            # Store all treatments in a separate attribute
            self.all_treatments = treatments if len(treatments) > 0 else []

            return subject_info_df

        except Exception as e:
            raise Exception(f"Error generating subject info mapping: {str(e)}")

    def _get_variable_mapping(self, part: str) -> Dict[str, str]:
        """Get variable mappings for a specific part of the subject info."""
        part_df = self.subject_info_df[self.subject_info_df['part'] == part]
        return dict(zip(part_df['SASLabel'], part_df['FieldOID']))

    def generate_function(self, study_id: str) -> str:
        """Generate the subjectInfo R function."""
        try:
            # Get variable mappings for each part
            enr_vars = self._get_variable_mapping('enr_summary')
            subject_vars = self._get_variable_mapping('subject_summary')
            sd_vars = self._get_variable_mapping('sd_summary')
            dd_vars = self._get_variable_mapping('dd_summary')
            dm_vars = self._get_variable_mapping('dm_summary')
            rsrc_vars = self._get_variable_mapping('rsrc_summary')
            candig_vars = self._get_variable_mapping('candig_summary')

            # Get treatments from the ALS file
            treatments = getattr(self, 'all_treatments', [])
            treatments_str = ', '.join([f'"{t}"' for t in treatments]) if treatments else ""

            # Get the first treatment from the treatments list
            first_treatment = treatments[0] if treatments else ""

            # Determine if this is a phase 1 study based on study_id
            # Check if the third number from the end is 1 (e.g., in b_BGB-b3227_101, the '1' in '101')
            is_phase_1 = False
            if study_id:
                # Extract the last part of the study_id that contains numbers
                import re
                number_parts = re.findall(r'\d+', study_id)
                if number_parts:
                    last_number = number_parts[-1]
                    # Check if the third digit from the end is 1
                    if len(last_number) >= 3 and last_number[-3] == '1':
                        is_phase_1 = True

            # Check if phase, part, and cohort fields exist in the enrollment dataframe
            has_phase = False
            has_part = False
            has_cohort = False
            has_dose_level = False

            # Collect all fields for handling multiple variables
            cohort_fields = []
            part_fields = []
            phase_fields = []
            dose_level_fields = []

            # Check for phase, part, and cohort fields in the enrollment dataframe
            import re
            for field in self.subject_info_df[self.subject_info_df['part'] == 'enr_summary']['SASLabel'].unique():
                # Use case-insensitive regex patterns for more flexible matching
                if re.search(r'^Phase', field, re.IGNORECASE):
                    has_phase = True
                    phase_fields.append(field)
                elif re.search(r'^Part', field, re.IGNORECASE):
                    has_part = True
                    part_fields.append(field)
                elif re.search(r'^Cohort', field, re.IGNORECASE):
                    has_cohort = True
                    cohort_fields.append(field)
                elif re.search(r'^Dose level', field, re.IGNORECASE):
                    has_dose_level = True
                    dose_level_fields.append(field)

            # Extract table mappings
            ds_enr = self.table_mappings['ds_enr']
            subject = self.table_mappings['subject']
            sd = self.table_mappings['sd']
            dd = self.table_mappings['dd']
            dm = self.table_mappings['dm']
            rs_r = self.table_mappings['rs_r']
            mh_dx = self.table_mappings['mh_dx']

            # Get all rs_r and mh_dx tables
            rs_r_tables = self.table_mappings['rs_r_tables']
            mh_dx_tables = self.table_mappings['mh_dx_tables']

            # Extract variable mappings
            enr_vars = self._get_variable_mapping('enr_summary')
            subject_vars = self._get_variable_mapping('subject_summary')
            sd_vars = self._get_variable_mapping('sd_summary')
            dd_vars = self._get_variable_mapping('dd_summary')
            dm_vars = self._get_variable_mapping('dm_summary')
            rsrc_vars = self._get_variable_mapping('rsrc_summary')
            candig_vars = self._get_variable_mapping('candig_summary')

            # Get specific variable names
            # Check for enrollment question with different possible wordings
            enr_was_subject_enrolled = enr_vars.get('Was the subject enrolled in the study?',
                                                   enr_vars.get('Was the subject randomized in the study?', ' '))
            subject_country = subject_vars.get('Country', ' ')
            # Handle multiple possible label names for date of subject completion
            sd_date_completion_fields = [
                'Date of subject completion/discontinuation from the study',
                'Date of subject completion from the study'
            ]

            # Check for each possible date field and use the first one found
            sd_date_of_subject_completion = ' '
            for field in sd_date_completion_fields:
                if field in sd_vars:
                    sd_date_of_subject_completion = sd_vars.get(field)
                    break
            sd_primary_reason_for_discontinuation = sd_vars.get('Indicate primary reason for subject discontinuation from the study', ' ')
            sd_other_reason_for_discontinuation = sd_vars.get('Other, specify', ' ')
            dd_date_of_death = dd_vars.get('Date of death', ' ')
            dd_primary_cause_of_death = dd_vars.get('Primary cause of death', ' ')
            dd_other_cause_of_death = dd_vars.get('Other, specify', ' ')
            dm_age = dm_vars.get('Age', ' ')
            dm_sex = dm_vars.get('Sex', ' ')
            dm_child_bearing_potential = dm_vars.get('Is subject of child-bearing potential?', ' ')
            dm_ethnicity = dm_vars.get('Ethnicity', ' ')
            dm_asian = dm_vars.get('Asian', ' ')
            dm_american_indian_or_alaska_native = dm_vars.get('American Indian or Alaska Native', ' ')
            dm_black_or_african_american = dm_vars.get('Black or African American', ' ')
            dm_native_hawaiian_or_other_pacific_islander = dm_vars.get('Native Hawaiian or other Pacific Islander', ' ')
            dm_white = dm_vars.get('White', ' ')
            dm_not_reported = dm_vars.get('Not reported', ' ')
            dm_unknown = dm_vars.get('Unknown', ' ')
            dm_other = dm_vars.get('Other', ' ')
            # Define possible date of response field names
            rsrc_date_fields = [
                'Date of response',
                'Date of overall response'
            ]

            # Check for each possible date field and use the first one found
            rsrc_date_of_response = ' '
            for field in rsrc_date_fields:
                if field in rsrc_vars:
                    rsrc_date_of_response = rsrc_vars.get(field)
                    break

            # Define possible medical history term field names
            candig_term_fields = [
                'Medical history term',
                'Type of solid tumor',
                'Disease type at study entry',
                'Disease type of initial diagnosis'

            ]

            # Check for each possible term field and use the first one found
            candig_medical_history_term = ' '
            for field in candig_term_fields:
                if field in candig_vars:
                    candig_medical_history_term = candig_vars.get(field)
                    break

            # Generate R code for processing multiple rs_r tables
            rs_r_processing_code = ""

            # Generate parameter strings for Roxygen header, function parameters, and examples
            rs_r_params = ""
            rs_r_function_params = ""
            rs_r_examples_params = ""
            rs_r_assertions = ""

            # Skip the first rs_r table as it's already included in the main parameters
            for i, table in enumerate(rs_r_tables[1:], 1):
                rs_r_params += f"#' @param {table} This is another response assessment dataframe\n"
                rs_r_function_params += f"{table}, "
                rs_r_examples_params += f"{table} = {table}, "
                rs_r_assertions += f"""
      # Assert {table} has min.rows and min.cols
      checkmate::assert_data_frame({table}, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm {table} has min.rows and min.cols."))"""

            # Generate code for each table
            for i, table in enumerate(rs_r_tables):
                table_var = f"rs_{i+1}"
                # Extract the date field from the mapping table based on the form name
                date_field = " "  # Default value
                # Filter for fields in the rsrc_summary part and match the form name
                rsrc_fields = self.subject_info_df[
                    (self.subject_info_df['part'] == 'rsrc_summary') &
                    (self.subject_info_df['Form'] == table.lower())
                ]

                # Define possible date field names
                date_field_names = [
                    'Date of response',
                    'Date of overall response'
                ]

                # Look for date fields
                for field in rsrc_fields['SASLabel'].unique():
                    for date_field_name in date_field_names:
                        if date_field_name in field:
                            date_field = rsrc_fields[rsrc_fields['SASLabel'] == field]['FieldOID'].iloc[0]
                            break
                    if date_field != " ":  # If we found a match, break out of the outer loop
                        break

                rs_r_processing_code += f"""
      {table_var}_summary <- {table} %>%
        dplyr::select(`Subject name or identifier` = .data$Subject,
                      `Date of Response Interpolated` = .data${date_field}_INT) %>%
        dplyr::filter(!is.na(.data$`Subject name or identifier`)) %>%
        dplyr::group_by(.data$`Subject name or identifier`) %>%
        dplyr::summarise(FirstResponseDate = min(.data$`Date of Response Interpolated`, na.rm = T),
                         LastResponseDate = max(.data$`Date of Response Interpolated`, na.rm = T))
      """

            # Add code to combine all rs_r tables
            if len(rs_r_tables) > 1:
                rs_r_bind_code = "      rsrc_summary <- dplyr::bind_rows(" + ", ".join([f"rs_{i+1}_summary" for i in range(len(rs_r_tables))]) + ")"
            else:
                rs_r_bind_code = "      rsrc_summary <- rs_1_summary"

            # Generate R code for processing multiple mh_dx tables
            mh_dx_processing_code = ""

            # Generate parameter strings for Roxygen header, function parameters, and examples
            mh_dx_params = ""
            mh_dx_function_params = ""
            mh_dx_examples_params = ""
            mh_dx_assertions = ""

            # Skip the first mh_dx table as it's already included in the main parameters
            for i, table in enumerate(mh_dx_tables[1:], 1):
                mh_dx_params += f"#' @param {table} This is another medical history dataframe\n"
                mh_dx_function_params += f"{table}, "
                mh_dx_examples_params += f"{table} = {table}, "
                mh_dx_assertions += f"""
      # Assert {table} has min.rows and min.cols
      checkmate::assert_data_frame({table}, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm {table} has min.rows and min.cols."))"""

            # Generate code for each table
            for i, table in enumerate(mh_dx_tables):
                table_var = f"dhx_{i+1}"
                # Extract the medical history term field from the mapping table based on the form name
                term_field = " "  # Default value
                # Filter for fields in the candig_summary part and match the form name
                candig_fields = self.subject_info_df[
                    (self.subject_info_df['part'] == 'candig_summary') &
                    (self.subject_info_df['Form'] == table.lower())
                ]

                # Define possible medical history term field names
                term_field_names = [
                    'Medical history term',
                    'Type of solid tumor',
                    'Disease type at study entry',
                    'Disease type of initial diagnosis'
                ]

                # Look for medical history term fields
                for field in candig_fields['SASLabel'].unique():
                    for term_field_name in term_field_names:
                        if term_field_name in field:
                            term_field = candig_fields[candig_fields['SASLabel'] == field]['FieldOID'].iloc[0]
                            break
                    if term_field != " ":  # If we found a match, break out of the outer loop
                        break

                # Check if this is a disease history table
                # Check if tumor type is "Solid Tumor"
                if self.tumor_type == "Solid Tumor":
                    # For Solid Tumor, use the extracted term field
                    mh_dx_processing_code += f"""
      {table_var}_summary <- {table} %>%
        dplyr::select(.data$Subject, .data${term_field})
      attr({table_var}_summary${term_field}, "label") <- "Type of Tumor"
      {table_var}_summary <- GSDSUtilities::raiseLabels({table_var}_summary, "label", isNullC = NA)
      """
                else:
                    # For Heme or other tumor types, use a more complex approach with multiple fields
                    mh_dx_processing_code += f"""
      {table_var}_summary <- {table} %>%
        dplyr::select(.data$Subject, .data${term_field})
      attr({table_var}_summary${term_field}, "label") <- "DiseaseType"
      {table_var}_summary <- GSDSUtilities::raiseLabels({table_var}_summary, "label", isNullC = NA)
      """

            # Add code to combine all mh_dx tables
            if len(mh_dx_tables) > 1:
                mh_dx_bind_code = "      candig_summary <- dplyr::bind_rows(" + ", ".join([f"dhx_{i+1}_summary" for i in range(len(mh_dx_tables))]) + ")"
            else:
                mh_dx_bind_code = "      candig_summary <- dhx_1_summary"

            # Handle multiple fields - use the first one found for each type
            selected_phase_field = phase_fields[0] if phase_fields else None
            selected_part_field = part_fields[0] if part_fields else None
            selected_cohort_field = cohort_fields[0] if cohort_fields else None
            selected_dose_level_field = dose_level_fields[0] if dose_level_fields else None

            # Generate dynamic cohort mutate statement and enrollment code
            cohort_mutate_code = ""
            cohort_select_field = ""

            if has_cohort and len(cohort_fields) > 1:
                # Multiple cohort fields - generate mutate statement to consolidate them
                cohort_oids = [enr_vars.get(field, field) for field in cohort_fields]

                # Generate case_when conditions for each cohort field
                case_when_conditions = []
                for i, cohort_oid in enumerate(cohort_oids):
                    # Create condition: this cohort is not NA/empty AND all others are NA/empty
                    this_condition = f"!is.na(.data${cohort_oid}) & .data${cohort_oid} != \"\""
                    other_conditions = []
                    for j, other_oid in enumerate(cohort_oids):
                        if i != j:  # Skip the current cohort
                            other_conditions.append(f"(is.na(.data${other_oid}) | .data${other_oid} == \"\")")

                    full_condition = this_condition + " & " + " & ".join(other_conditions) + f" ~ .data${cohort_oid}"
                    case_when_conditions.append(full_condition)

                # Add TRUE condition for fallback
                case_when_conditions.append("TRUE ~ NA_character_")

                # Create the mutate code with proper newlines
                conditions_str = ',\n          '.join(case_when_conditions)
                cohort_mutate_code = f'''dplyr::mutate(
        Cohort = dplyr::case_when(
          {conditions_str}
        )) %>%
        '''
                cohort_select_field = "Cohort"

            elif has_cohort and selected_cohort_field:
                # Single cohort field - use it directly
                cohort_field_oid = enr_vars.get(selected_cohort_field, 'Cohort')
                cohort_select_field = cohort_field_oid

            # Generate cohort enrollment and treatment status code
            if has_cohort and cohort_select_field:
                cohort_enrollment_code = f'''# Calculate Cohort Enrollment
      CohortEnroll <- SubjectInfo %>%
        dplyr::select(.data$`Subject name or identifier`, .data${cohort_select_field}, .data$Enrolled) %>%
        dplyr::filter(substr(.data$Enrolled, 1, 1) == "Y") %>%
        dplyr::group_by(.data${cohort_select_field}) %>%
        dplyr::summarise(`TotalEnrolled by Cohort/Arm` = dplyr::n_distinct(.data$`Subject name or identifier`)) %>%
        dplyr::distinct()

      SubjectInfo <- dplyr::left_join(SubjectInfo, CohortEnroll, by = "{cohort_select_field}")'''

                treatment_status_code = f'''# With Cohort
      Treated <- SubjectInfo %>%
        dplyr::filter(!is.na(.data$FirstDoseDate)) %>%
        dplyr::select(.data$`Subject name or identifier`, .data${cohort_select_field}) %>%
        dplyr::mutate(TotalTreated = dplyr::n_distinct(.data$`Subject name or identifier`),
                      Treated = "Yes") %>%
        dplyr::distinct()

      SubjectInfo <- dplyr::left_join(SubjectInfo, Treated, by = c("Subject name or identifier", "{cohort_select_field}"))

      CohortTreated <- Treated %>%
        dplyr::group_by(.data${cohort_select_field}) %>%
        dplyr::summarise(`TotalTreated by Cohort/Arm` = dplyr::n_distinct(.data$`Subject name or identifier`)) %>%
        dplyr::distinct()

      SubjectInfo <- dplyr::left_join(SubjectInfo, CohortTreated, by = "{cohort_select_field}")'''
            else:
                cohort_enrollment_code = '# Cohort Enrollment calculation is skipped because Cohort field does not exist'
                treatment_status_code = '''# Without Cohort
      Treated <- SubjectInfo %>%
        dplyr::filter(!is.na(.data$FirstDoseDate)) %>%
        dplyr::select(.data$`Subject name or identifier`) %>%
        dplyr::mutate(TotalTreated = dplyr::n_distinct(.data$`Subject name or identifier`),
                      Treated = "Yes") %>%
        dplyr::distinct()

      SubjectInfo <- dplyr::left_join(SubjectInfo, Treated, by = "Subject name or identifier")'''



            # Template for the R function with placeholders
            # Define the template as a regular string, not an f-string
            template = """#' @title subjectInfo_{study_id}
#' @description Create a Subject Info file and dataframe
#'
#' @param studyId This is a character field, studyId
#' @param sourceLocation This is a character field, study analysis directory
#' @param tempLogger This is the logger for the whole job, creating one logfile per jobdatetime
#' @param jobdatetime This is a character field, datetime of the jobrun, format Y_m_d_H_M_OS3
#' @param dose_merged This the dose_merged dataframe
#' @param {self_table_mappings_ds_enr} This is the enrollment dataframe
#' @param {self_table_mappings_subject} This is the subject dataframe
#' @param {self_table_mappings_sd} This is the study discontinuation dataframe
#' @param {self_table_mappings_dd} This is the death dataframe
#' @param td_merged This is the td_merged dataframe
#' @param {self_table_mappings_dm} This is the demographics dataframe
#' @param {self_table_mappings_rs_r} This is the response assessment dataframe
{rs_r_params}
#' @param {self_table_mappings_mh_dx} This is the medical history dataframe
{mh_dx_params}
#' @param develop.f disable email notification for testing
#' @param vpath This is the location of vault
#'
#' @return SubjectInfo
#'
#' @export subjectInfo_{study_id}
#'
#' @importFrom dplyr select distinct mutate case_when filter group_by summarise left_join vars mutate_at n_distinct count pull
#' @importFrom GSDSUtilities raiseLabels
#' @importFrom tidyr pivot_wider
#' @importFrom writexl write_xlsx
#' @importFrom magrittr %>%
#' @importFrom checkmate assert_data_frame assert_directory_exists
#' @importFrom log4r info warn error
#' @importFrom mailR send.mail
#' @importFrom secret get_secret local_key
#' @importFrom rlang .data :=
#' @importFrom lubridate as_datetime
#'
#' @examples
#' \\dontrun{{
#'subjectInfo_{study_id}(studyId = studyIdVar, sourceLocation = OutFolder,
#'                           tempLogger = CentralLogger, jobdatetime = jobdatetime,
#'                           dose_merged = dose_merged, {self_table_mappings_ds_enr} = {self_table_mappings_ds_enr}, {self_table_mappings_subject} = {self_table_mappings_subject},
#'                           {self_table_mappings_sd} = {self_table_mappings_sd}, {self_table_mappings_dd} = {self_table_mappings_dd}, td_merged = td_merged, {self_table_mappings_dm} = {self_table_mappings_dm},
#'                           {self_table_mappings_rs_r} = {self_table_mappings_rs_r}, {rs_r_examples_params}
#'                           {self_table_mappings_mh_dx} = {self_table_mappings_mh_dx}, {mh_dx_examples_params}
#'                           develop.f = develop.f, vpath = vpath)
#' }}
#'
subjectInfo_{study_id} <- function(studyId, sourceLocation, tempLogger, jobdatetime = jobdatetime, dose_merged,
                                       {self_table_mappings_ds_enr}, {self_table_mappings_subject}, {self_table_mappings_sd}, {self_table_mappings_dd}, td_merged, {self_table_mappings_dm},
                                       {self_table_mappings_rs_r}, {rs_r_function_params}
                                       {self_table_mappings_mh_dx}, {mh_dx_function_params}
                                       develop.f = develop.f, vpath = vpath) {{
  withCallingHandlers(
    expr = {{
      calledFun = "subjectInfo_{study_id}"
      # Start Function --------------------------------------------------------------------
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- Started."))

      # Assertions -----------------------------------------------------------------------
      # Assert dose_merged has min.rows and min.cols
      checkmate::assert_data_frame(dose_merged, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm dose_merged has min.rows and min.cols."))
      # Assert {self_table_mappings_ds_enr} has min.rows and min.cols
      checkmate::assert_data_frame({self_table_mappings_ds_enr}, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm {self_table_mappings_ds_enr} has min.rows and min.cols."))
      # Assert {self_table_mappings_subject} has min.rows and min.cols
      checkmate::assert_data_frame({self_table_mappings_subject}, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm {self_table_mappings_subject} has min.rows and min.cols."))
      # Assert {self_table_mappings_sd} has min.rows and min.cols
      checkmate::assert_data_frame({self_table_mappings_sd}, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm {self_table_mappings_sd} has min.rows and min.cols."))
      # Assert {self_table_mappings_dd} has min.rows and min.cols
      checkmate::assert_data_frame({self_table_mappings_dd}, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm {self_table_mappings_dd} has min.rows and min.cols."))
      # Assert td_merged has min.rows and min.cols
      checkmate::assert_data_frame(td_merged, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm td_merged has min.rows and min.cols."))
      # Assert {self_table_mappings_dm} has min.rows and min.cols
      checkmate::assert_data_frame({self_table_mappings_dm}, min.rows = 1, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm {self_table_mappings_dm} has min.rows and min.cols."))
      # Assert {self_table_mappings_rs_r} has min.rows and min.cols
      checkmate::assert_data_frame({self_table_mappings_rs_r}, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm {self_table_mappings_rs_r} has min.rows and min.cols."))
      {rs_r_assertions}
      # Assert {self_table_mappings_mh_dx} has min.rows and min.cols
      checkmate::assert_data_frame({self_table_mappings_mh_dx}, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm {self_table_mappings_mh_dx} has min.rows and min.cols."))
      {mh_dx_assertions}
      # Assert sourceLocation directory exists
      checkmate::assert_directory_exists(sourceLocation)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," The sourceLocation directory exists."))

      # enr_summary Table -------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating enr_summary dataframe"))
      enr_summary <- {self_table_mappings_ds_enr} %>%
        {cohort_mutate_code}dplyr::select(.data$Subject, .data${enr_was_subject_enrolled}{has_phase_code}{has_part_code}{has_cohort_code}{has_dose_level_code})
      attr(enr_summary${enr_was_subject_enrolled}, "label") <- "Enrolled"
      enr_summary <- GSDSUtilities::raiseLabels(enr_summary, "label", isNullC = NA)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","raiseLabels of enr_summary dataframe"))

      # subject_summary Table ----------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating subject_summary dataframe"))
      subject_summary <- {self_table_mappings_subject} %>%
        dplyr::select(.data$Subject, .data${subject_country})
      attr(subject_summary${subject_country}, "label") <- "Country"
      subject_summary <- GSDSUtilities::raiseLabels(subject_summary, "label", isNullC = NA)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","raiseLabels of subject_summary dataframe"))

      # sd_summary Table --------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating sd_summary dataframe"))
      sd_summary <- {self_table_mappings_sd} %>%
        dplyr::select(.data$Subject, .data${sd_date_of_subject_completion}_INT,
                     .data${sd_primary_reason_for_discontinuation},
                     .data${sd_other_reason_for_discontinuation}) %>%
        dplyr::mutate(`EOS Reason` = dplyr::if_else(.data${sd_primary_reason_for_discontinuation} == "Other",
                                                   .data${sd_other_reason_for_discontinuation},
                                                   .data${sd_primary_reason_for_discontinuation})) %>%
        dplyr::select(-.data${sd_other_reason_for_discontinuation})
      attr(sd_summary${sd_date_of_subject_completion}_INT, "label") <- "EOS Date"
      sd_summary <- GSDSUtilities::raiseLabels(sd_summary, "label", isNullC = NA)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","raiseLabels of sd_summary dataframe"))

      # dd_summary Table --------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating dd_summary dataframe"))
      dd_summary <- {self_table_mappings_dd} %>%
        dplyr::select(.data$Subject, .data${dd_date_of_death}_INT,
                     .data${dd_primary_cause_of_death},
                     .data${dd_other_cause_of_death}) %>%
        dplyr::mutate(`Death Cause` = dplyr::if_else(.data${dd_primary_cause_of_death} == "Other",
                                                   .data${dd_other_cause_of_death},
                                                   .data${dd_primary_cause_of_death})) %>%
        dplyr::select(-.data${dd_other_cause_of_death})
      attr(dd_summary${dd_date_of_death}_INT, "label") <- "Death Date"
      dd_summary <- GSDSUtilities::raiseLabels(dd_summary, "label", isNullC = NA)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","raiseLabels of dd_summary dataframe"))

      # td_tmp Table -----------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating td_tmp dataframe"))
      treatments <- c({treatments_str})

      eot_reason_col_names <- paste0("EOT Reason -", treatments)

      if(nrow(td_merged) == 0){{
        td_tmp <- tibble::tibble("Subject name or identifier" = character(0))

        for (treat_name in eot_reason_col_names) {{
          td_tmp[[treat_name]] <- character(0)
        }}

      }}else{{
        td_tmp <- td_merged %>%
        dplyr::select(.data$`Subject name or identifier`, .data$`EOT Reason`, .data$Treatment)  %>%
        tidyr::pivot_wider(id_cols = .data$`Subject name or identifier`, names_from = .data$Treatment,
                           names_prefix = "EOT Reason -",values_from = "EOT Reason") %>%
        dplyr::distinct()

        if(!all(eot_reason_col_names) %in% names(td_tmp)){{
          cols_to_add <- setdiff(eot_reason_col_names, names(td_tmp))

          for (col_name in cols_to_add) {{
            td_tmp <- td_tmp %>%
              dplyr::mutate(!!col_name := NA_character_)
          }}
        }}
      }}

      # dm_summary Table ----------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating dm_summary dataframe"))
      dm_summary <- {self_table_mappings_dm} %>%
        dplyr::select(.data$Subject, .data${dm_age}, .data${dm_sex}, .data${dm_child_bearing_potential}, .data${dm_ethnicity},
                      .data${dm_asian}, .data${dm_american_indian_or_alaska_native},
                      .data${dm_black_or_african_american}, .data${dm_native_hawaiian_or_other_pacific_islander},
                      .data${dm_white}, .data${dm_not_reported}, .data${dm_unknown},
                      .data${dm_other}, .data$SiteNumber, .data$Site, .data$project) %>%
        dplyr::mutate(rcsum = rowSums(dplyr::select(., .data${dm_asian}, .data${dm_american_indian_or_alaska_native},
                                                    .data${dm_black_or_african_american}, .data${dm_native_hawaiian_or_other_pacific_islander},
                                                    .data${dm_white}, .data${dm_other}))) %>%
        dplyr::mutate(Race = dplyr::case_when(.data$rcsum > 1 ~ "Multiple",
                                              .data${dm_american_indian_or_alaska_native} == 1 ~ "American Indian or Alaska Native",
                                              .data${dm_asian} == 1 ~ "Asian" ,
                                              .data${dm_black_or_african_american} == 1 ~ "Black or African American" ,
                                              .data${dm_native_hawaiian_or_other_pacific_islander} == 1 ~ "Native Hawaiian or Other Pacific Islander",
                                              .data${dm_white} == 1 ~ "White",
                                              .data${dm_other} == 1 ~ "Other",
                                              .data${dm_not_reported} == 1 ~ "Not Reported",
                                              .data${dm_unknown} == 1 ~ "Unknown"
        )) %>% dplyr::select(.data$Subject, .data${dm_age}, .data${dm_sex}, .data${dm_child_bearing_potential},
                            .data${dm_ethnicity}, .data$Race, .data$SiteNumber, .data$Site, .data$project)
      attr(dm_summary${dm_age}, "label") <- "Age"
      attr(dm_summary${dm_sex}, "label") <- "Sex"
      attr(dm_summary${dm_child_bearing_potential}, "label") <- "Child-bearing potential"
      attr(dm_summary${dm_ethnicity}, "label") <- "Ethnicity"
      dm_summary <- GSDSUtilities::raiseLabels(dm_summary, "label", isNullC = NA)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","raiseLabels of dm_summary dataframe"))

      # First Dose Date  ex_FirstDoseDate -------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating ex_summary dataframe"))
      ex_FirstDoseDate <- dose_merged %>%
        dplyr::filter(!is.na(.data$`Subject name or identifier`)) %>%
        dplyr::filter(!is.na(.data$`Planned Dose`)) %>%
        dplyr::filter(.data$`Actual Dose` != "0mg" | .data$`Actual Dose` != "0" | .data$`Actual Dose` != "0 mg" ) %>%
        dplyr::group_by(.data$`Subject name or identifier`) %>%
        dplyr::summarise(FirstDoseDate = if (all(is.na(.data$`Dose Start Date (Interpolated)`))) lubridate::as_datetime(NA)
                         else min(.data$`Dose Start Date (Interpolated)`, na.rm = T))

      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating ex_LastDoseDate dataframe"))
      ex_LastDoseDate <- dose_merged %>%
        dplyr::filter(!is.na(.data$`Subject name or identifier`)) %>%
        dplyr::filter(!is.na(.data$`Planned Dose`)) %>%
        dplyr::filter(.data$`Actual Dose` != "0mg" | .data$`Actual Dose` != "0" | .data$`Actual Dose` != "0 mg" ) %>%
        dplyr::group_by(.data$`Subject name or identifier`) %>%
        dplyr::summarise(MaxEndDate = if (all(is.na(.data$`Dose End Date (Interpolated)`))) lubridate::as_datetime(NA)
                         else max(.data$`Dose End Date (Interpolated)`, na.rm = T),
                         MaxStartDate = if (all(is.na(.data$`Dose Start Date (Interpolated)`))) lubridate::as_datetime(NA)
                         else max(.data$`Dose Start Date (Interpolated)`, na.rm = T)) %>%
        dplyr::mutate(LastDoseDate = dplyr::case_when(.data$MaxStartDate > .data$MaxEndDate ~ .data$MaxStartDate,
                                                      .data$MaxEndDate >= .data$MaxStartDate ~ .data$MaxEndDate)) %>%
        dplyr::select(.data$`Subject name or identifier`, .data$LastDoseDate)

      {max_actual_dose_code}

      # ex_summary by joining ex_FirstDoseDate ex_LastDoseDate ---------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating ex_summary dataframe"))
      ex_summary <- dplyr::left_join(ex_FirstDoseDate,ex_LastDoseDate,by = "Subject name or identifier")
      {max_actual_dose_join}
      attr(ex_summary$`Subject name or identifier`, "label") <- "Subject name or identifier"

      # First and Last Response Date Calculation by --------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating rsrc_FirstLast dataframe"))
      {rs_r_processing_code}

      {rs_r_bind_code}

      # EOT Date --------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating EOTDbyTreatment dataframe"))
      eot_date_col_names <- paste0("EOT Date -", treatments)
      if(nrow(td_merged) == 0){{
        EOTDbyTreatment <- tibble::tibble("Subject name or identifier" = character(0))

        for (col_name in eot_date_col_names) {{
          EOTDbyTreatment[[col_name]] <- as.Date(x = integer(0),
                                                 origin = "1970-01-01")
        }}
      }}else{{
        EOTDbyTreatment <- td_merged %>%
          dplyr::select(.data$`EOT Date`, .data$`Subject name or identifier`, .data$Treatment) %>%
          dplyr::mutate(`EOT Date` = as.character(.data$`EOT Date`)) %>%
          tidyr::pivot_wider(id_cols = .data$`Subject name or identifier`, names_from = .data$Treatment,
                             names_prefix = "EOT Date -",values_from = "EOT Date") %>%
          dplyr::mutate_at(dplyr::vars(grep("Date", names(.), value = TRUE)), .funs = as.Date)

        if (!all(eot_date_col_names) %in% names(EOTDbyTreatment)) {{
          cols_to_add <- setdiff(eot_date_col_names, names(EOTDbyTreatment))

          for (col_name in cols_to_add) {{
            EOTDbyTreatment <- EOTDbyTreatment %>%
              dplyr::mutate(!!col_name := as.Date(x = NA_character_,
                                                  origin = "1970-01-01"))
          }}
        }}
      }}

      # td_summary --------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating td_summary dataframe"))
      td_summary <- dplyr::left_join(EOTDbyTreatment, td_tmp,by = "Subject name or identifier")
      attr(td_summary$`Subject name or identifier`, "label") <-  "Subject name or identifier"

      # candig_summary Table ----------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating candig_summary dataframe"))
      {mh_dx_processing_code}

      {mh_dx_bind_code}
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","raiseLabels of candig_summary dataframe"))

      # SubjectInfo dataframe -------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating SubjectInfo dataframe"))
      SubjectInfo <- dplyr::left_join(dm_summary, enr_summary, by = "Subject name or identifier")
      SubjectInfo <- dplyr::left_join(SubjectInfo, subject_summary, by = "Subject name or identifier")
      SubjectInfo <- dplyr::left_join(SubjectInfo, rsrc_summary, by = "Subject name or identifier")
      SubjectInfo <- dplyr::left_join(SubjectInfo, sd_summary, by = "Subject name or identifier")
      SubjectInfo <- dplyr::left_join(SubjectInfo, dd_summary, by = "Subject name or identifier")
      SubjectInfo <- dplyr::left_join(SubjectInfo, td_summary, by = "Subject name or identifier")
      SubjectInfo <- dplyr::left_join(SubjectInfo, ex_summary, by = "Subject name or identifier")
      SubjectInfo <- dplyr::left_join(SubjectInfo, candig_summary, by = "Subject name or identifier")

      # Calculate SiteEnrolled, TotalEnrolled, and # of Females Enrolled
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Calculating EnrollmentbySite"))
      EnrollmentbySite <- SubjectInfo %>%
        dplyr::select(.data$`Subject name or identifier`, .data$SiteNumber, .data$Enrolled) %>%
        dplyr::filter(substr(.data$Enrolled, 1, 1) == "Y") %>%
        dplyr::group_by(.data$SiteNumber) %>%
        dplyr::summarise(SiteEnrolled = dplyr::n_distinct(.data$`Subject name or identifier`)) %>%
        dplyr::distinct()

      SubjectInfo <- dplyr::left_join(SubjectInfo, EnrollmentbySite, by = "SiteNumber") %>%
        dplyr::mutate(TotalEnrolled = (SubjectInfo %>%
                                         dplyr::filter(substr(.data$Enrolled, 1, 1) == "Y") %>%
                                         dplyr::select(.data$`Subject name or identifier`) %>%
                                         dplyr::distinct() %>%
                                         dplyr::count() %>%
                                         dplyr::pull())) %>%
        dplyr::mutate(TotalFemalesEnrolled = (SubjectInfo %>%
                                               dplyr::filter(substr(.data$Enrolled, 1, 1) == "Y" & substr(.data$Sex, 1, 1) == "F") %>%
                                               dplyr::select(.data$`Subject name or identifier`) %>%
                                               dplyr::distinct() %>%
                                               dplyr::count() %>%
                                               dplyr::pull()))

      # Calculate Active Treatment Status
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Calculate OnTreatment Status"))
      SubjectInfo <- SubjectInfo %>%
        dplyr::mutate(`OnTreatment` = dplyr::case_when(
          substr(.data$Enrolled, 1, 1) == "Y" & (!is.na(.data$`EOT Date -{first_treatment_placeholder}`) |  !is.na(.data$`EOS Date`) | !is.na(.data$`Death Date`)) ~ "N",
          substr(.data$Enrolled, 1, 1) == "Y" & is.na(.data$`EOT Date -{first_treatment_placeholder}`) ~ "Y",
          substr(.data$Enrolled, 1, 1) == "N" ~ "Not Enrolled"
        )) %>%
        dplyr::select(-.data$project, tidyselect::everything())

      {cohort_enrollment_code}

      # Calculate Treatment Status
      {treatment_status_code}

      # Assign SubjectInfo to calling envir
      assign("SubjectInfo", SubjectInfo, envir = parent.frame())
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," SubjectInfo returned"))

      # End of subjectInfo Function
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))
    }},
    error = function(e) {{
      log4r::error(tempLogger, paste0(studyId,"|",calledFun,"|",e))
      stop(e)
    }},
    warning = function(w) {{
      log4r::warn(tempLogger, paste0(studyId,"|",calledFun,"|",w))
    }}
  )
}}"""
            # Replace placeholders with values
            template = template.format(
                study_id=study_id,
                treatments_str=treatments_str if treatments_str else '""',
                first_treatment=first_treatment,
                has_phase_code=', .data' + enr_vars.get(selected_phase_field, ' ') if has_phase and selected_phase_field else '',
                has_part_code=', .data' + enr_vars.get(selected_part_field, ' ') if has_part and selected_part_field else '',
                has_cohort_code=', .data$Cohort' if has_cohort and len(cohort_fields) > 1 else (', .data' + enr_vars.get(selected_cohort_field, ' ') if has_cohort and selected_cohort_field else ''),
                has_dose_level_code=', .data' + enr_vars.get(selected_dose_level_field, ' ') if has_dose_level and selected_dose_level_field else '',
                max_actual_dose_code='# Max Actual Dose ----\n      MaxActualDose <- dose_merged %>%\n        dplyr::filter(!is.na(.data$`Subject name or identifier`), .data$Treatment == "' + first_treatment + '") %>%\n        dplyr::filter(!is.na(.data$`Planned Dose`)) %>%\n        dplyr::filter(.data$`Actual Dose` != "0") %>%\n        dplyr::mutate(`ActualDoseNumeric` = as.numeric(stringr::str_extract(.data$`Actual Dose`, pattern = "(\\\\d|\\\\.)*"))) %>%\n        dplyr::group_by(.data$`Subject name or identifier`) %>%\n        dplyr::summarise(MaxActualDose = max(.data$`ActualDoseNumeric`, na.rm = T) ) %>%\n        dplyr::select(.data$`Subject name or identifier`, .data$MaxActualDose)' if is_phase_1 else '# MaxActualDose calculation is skipped for non-phase 1 studies',
                max_actual_dose_join='ex_summary <- dplyr::left_join(ex_summary, MaxActualDose, by = "Subject name or identifier")' if is_phase_1 else '# MaxActualDose join is skipped for non-phase 1 studies',
                cohort_mutate_code=cohort_mutate_code,
                cohort_enrollment_code=cohort_enrollment_code,
                treatment_status_code=treatment_status_code,
                # Table mappings
                self_table_mappings_ds_enr=ds_enr,
                self_table_mappings_subject=subject,
                self_table_mappings_sd=sd,
                self_table_mappings_dd=dd,
                self_table_mappings_dm=dm,
                self_table_mappings_rs_r=rs_r,
                self_table_mappings_mh_dx=mh_dx,
                # Multiple table processing code
                rs_r_processing_code=rs_r_processing_code,
                rs_r_bind_code=rs_r_bind_code,
                mh_dx_processing_code=mh_dx_processing_code,
                mh_dx_bind_code=mh_dx_bind_code,
                # Parameter strings for Roxygen header, function parameters, and examples
                rs_r_params=rs_r_params,
                rs_r_function_params=rs_r_function_params,
                rs_r_examples_params=rs_r_examples_params,
                rs_r_assertions=rs_r_assertions,
                mh_dx_params=mh_dx_params,
                mh_dx_function_params=mh_dx_function_params,
                mh_dx_examples_params=mh_dx_examples_params,
                mh_dx_assertions=mh_dx_assertions,
                # Variable mappings
                enr_was_subject_enrolled=enr_was_subject_enrolled,
                subject_country=subject_country,
                sd_date_of_subject_completion=sd_date_of_subject_completion,
                sd_primary_reason_for_discontinuation=sd_primary_reason_for_discontinuation,
                sd_other_reason_for_discontinuation=sd_other_reason_for_discontinuation,
                dd_date_of_death=dd_date_of_death,
                dd_primary_cause_of_death=dd_primary_cause_of_death,
                dd_other_cause_of_death=dd_other_cause_of_death,
                dm_age=dm_age,
                dm_sex=dm_sex,
                dm_child_bearing_potential=dm_child_bearing_potential,
                dm_ethnicity=dm_ethnicity,
                dm_asian=dm_asian,
                dm_american_indian_or_alaska_native=dm_american_indian_or_alaska_native,
                dm_black_or_african_american=dm_black_or_african_american,
                dm_native_hawaiian_or_other_pacific_islander=dm_native_hawaiian_or_other_pacific_islander,
                dm_white=dm_white,
                dm_not_reported=dm_not_reported,
                dm_unknown=dm_unknown,
                dm_other=dm_other,
                rsrc_date_of_response=rsrc_date_of_response,
                candig_medical_history_term=candig_medical_history_term,
                first_treatment_placeholder=first_treatment
            )

            return template

        except Exception as e:
            raise Exception(f"Error generating function: {str(e)}")